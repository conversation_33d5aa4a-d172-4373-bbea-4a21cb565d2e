<!-- Desktop Students Status Bar Component - Inline in header -->
<div class="students-status-bar desktop-status-bar" id="students-status-bar-desktop">
    <div class="status-info">
        <span class="status-item">
            <span class="material-icons">group</span>
            <span id="total-count-desktop">{{ result_boys|add:result_girls }}</span> élèves
        </span>
        <span class="status-separator">•</span>
        <span class="status-item">
            <span class="material-icons">boy</span>
            <span id="boys-count-desktop">{{ result_boys }}</span> garçons
        </span>
        <span class="status-separator">•</span>
        <span class="status-item">
            <span class="material-icons">girl</span>
            <span id="girls-count-desktop">{{ result_girls }}</span> filles
        </span>
    </div>
</div>

<style>
/* Desktop Students Status Bar Styles - Inline in header */
.desktop-status-bar {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 13px;
    color: var(--text-secondary, #666);
    margin: 0 16px;
    flex: 1;
    justify-content: center;
}

.desktop-status-bar .status-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.desktop-status-bar .status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 16px;
}

.desktop-status-bar .status-item .material-icons {
    font-size: 26px;
    color: var(--text-secondary, #666);
    font-weight: bold;
}

.desktop-status-bar .status-separator {
    color: var(--text-disabled, #999);
    font-weight: bold;
}

.desktop-status-bar .status-filters {
    display: flex;
    align-items: center;
    gap: 8px;
}

.desktop-status-bar .status-filter {
    display: flex;
    align-items: center;
    gap: 4px;
    background: var(--primary-light, #e8f5e8);
    color: var(--primary, #4caf50);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.desktop-status-bar .status-filter .material-icons {
    font-size: 14px;
}

/* Hide desktop status bar on mobile */
@media (max-width: 768px) {
    .desktop-status-bar {
        display: none;
    }
}

/* Responsive adjustments for smaller desktop screens */
@media (min-width: 769px) and (max-width: 1200px) {
    .desktop-status-bar {
        font-size: 12px;
        gap: 12px;
    }
    
    .desktop-status-bar .status-info {
        gap: 10px;
    }
    
    .desktop-status-bar .status-item .material-icons {
        font-size: 14px;
    }
}
</style>

<script>
// Desktop Students Status Bar JavaScript Component
(function() {
    'use strict';

    // Update counts in desktop status bar
    function updateStudentsStatusCountsDesktop(boysCount, girlsCount) {
        const totalCountEl = document.getElementById('total-count-desktop');
        const boysCountEl = document.getElementById('boys-count-desktop');
        const girlsCountEl = document.getElementById('girls-count-desktop');
        
        if (totalCountEl) totalCountEl.textContent = boysCount + girlsCount;
        if (boysCountEl) boysCountEl.textContent = boysCount;
        if (girlsCountEl) girlsCountEl.textContent = girlsCount;
    }

    // Make functions globally available
    window.updateStudentsStatusBarDesktop = updateStudentsStatusBarDesktop;
    window.updateStudentsStatusCountsDesktop = updateStudentsStatusCountsDesktop;

    // Initialize status bar when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', updateStudentsStatusBarDesktop);
    } else {
        updateStudentsStatusBarDesktop();
    }

    // Update status bar after HTMX swaps
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'content-area' || 
            event.detail.target.querySelector && event.detail.target.querySelector('#students-status-bar-desktop')) {
            setTimeout(() => updateStudentsStatusBarDesktop(), 100);
        }
    });
})();
</script>
